import { MigrationInterface, QueryRunner } from "typeorm";

export class AddIsDisconnectedInChannel1748767124712 implements MigrationInterface {
    name = 'AddIsDisconnectedInChannel1748767124712'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "channel" ADD "chatbotEnabled" boolean NOT NULL DEFAULT true`);
        await queryRunner.query(`ALTER TABLE "channel" ADD "isDisconnected" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "channel" DROP COLUMN "isDisconnected"`);
        await queryRunner.query(`ALTER TABLE "channel" DROP COLUMN "chatbotEnabled"`);
    }

}
